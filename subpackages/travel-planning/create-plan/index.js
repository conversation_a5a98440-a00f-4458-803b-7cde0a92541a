// subpackages/travel-planning/create-plan/index.js

import travelService from '../../../utils/travel-service.js'
import userManager from '../../../utils/userManager.js'

Page({
  data: {
    // 当前步骤
    currentStep: 1,

    // 表单数据
    formData: {
      title: '',
      destination: '',
      coordinates: null,
      startDate: '',
      endDate: '',
      budget: '',
      notes: ''
    },

    // 人数选择器数据
    participantOptions: ['1人', '2人', '3人', '4人', '5人', '6人', '7人', '8人', '9人', '10人', '10人以上'],
    participantIndex: 0,

    // 计算属性
    duration: 0,
    today: '',
    canNextStep: false,

    // 状态
    loading: false,
    loadingText: '',
    saving: false,

    // {{ AURA-X: Modify - 改为邀请选项状态管理. Approval: 寸止(ID:1738056000). }}
    // 邀请协作状态
    enableCollaboration: false,
    showInviteOption: false,

    // {{ AURA-X: Add - 编辑模式状态管理. Approval: 寸止(ID:1738056000). }}
    // 编辑模式
    isEditMode: false,
    planId: null
  },

  // {{ AURA-X: Modify - 支持编辑模式，处理mode=edit参数. Approval: 寸止(ID:1738056000). }}
  onLoad(options) {
    // 检查是否为编辑模式
    if (options.mode === 'edit' && options.id) {
      this.setData({
        isEditMode: true,
        planId: options.id
      })
      this.loadPlanForEdit(options.id)
    } else {
      this.initPage()
    }
  },

  // 初始化页面
  initPage() {
    const today = new Date()
    const todayStr = this.formatDate(today)

    this.setData({
      today: todayStr
    })

    this.checkCanNextStep()
  },

  // {{ AURA-X: Add - 加载计划数据用于编辑模式. Approval: 寸止(ID:1738056000). }}
  // 加载计划数据用于编辑
  async loadPlanForEdit(planId) {
    try {
      // {{ AURA-X: Add - 设置编辑模式的页面标题. Approval: 寸止(ID:1738056000). }}
      wx.setNavigationBarTitle({
        title: '编辑旅行计划'
      })

      this.setData({ loading: true, loadingText: '加载计划数据...' })

      const result = await travelService.getTravelPlanDetail(planId)

      if (result.success && result.data) {
        const planData = result.data

        // 填充表单数据
        this.setData({
          'formData.title': planData.title || '',
          'formData.destination': planData.destination || '',
          'formData.coordinates': planData.coordinates || null,
          'formData.startDate': planData.startDate || '',
          'formData.endDate': planData.endDate || '',
          'formData.budget': planData.budget?.toString() || '',
          'formData.notes': planData.notes || '',
          participantIndex: Math.max(0, Math.min(planData.participants - 1, 10)) || 0,
          enableCollaboration: planData.enableCollaboration || false
        })

        // 初始化其他数据
        const today = new Date()
        this.setData({
          today: this.formatDate(today)
        })

        this.checkCanNextStep()
      } else {
        wx.showToast({
          title: '加载计划失败',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('加载计划数据失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    } finally {
      this.setData({ loading: false, loadingText: '' })
    }
  },

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  // 计算旅行天数
  calculateDuration() {
    const { startDate, endDate } = this.data.formData
    if (startDate && endDate) {
      const start = new Date(startDate)
      const end = new Date(endDate)
      const diffTime = Math.abs(end - start)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
      this.setData({ duration: diffDays })
    } else {
      this.setData({ duration: 0 })
    }
  },

  // 检查是否可以进入下一步
  checkCanNextStep() {
    const { currentStep, formData } = this.data
    let canNext = false
    
    if (currentStep === 1) {
      canNext = formData.title.trim() && formData.destination.trim()
    } else if (currentStep === 2) {
      canNext = formData.startDate && formData.endDate
    } else if (currentStep === 3) {
      canNext = true
    }
    
    this.setData({ canNextStep: canNext })
  },

  // 输入处理
  onTitleInput(e) {
    this.setData({
      'formData.title': e.detail.value
    })
    this.checkCanNextStep()
  },

  onBudgetInput(e) {
    this.setData({
      'formData.budget': e.detail.value
    })
  },

  onNotesInput(e) {
    this.setData({
      'formData.notes': e.detail.value
    })
  },

  // 日期选择
  onStartDateChange(e) {
    this.setData({
      'formData.startDate': e.detail.value
    })
    this.calculateDuration()
    this.checkCanNextStep()
  },

  onEndDateChange(e) {
    this.setData({
      'formData.endDate': e.detail.value
    })
    this.calculateDuration()
    this.checkCanNextStep()
  },

  // 人数选择器变化
  onParticipantChange(e) {
    const index = e.detail.value
    this.setData({
      participantIndex: index
    })
  },

  // {{ AURA-X: Modify - 直接显示真实邀请码弹窗. Approval: 寸止(ID:1738056000). }}
  // 显示邀请选项
  showInviteOption() {
    // 直接开启协作
    this.setData({
      enableCollaboration: true,
      showInviteOption: true
    })

    // 生成真实邀请码
    const inviteCode = this.generateTempInviteCode()
    const planTitle = this.data.formData.title || '我的旅行计划'

    // 调用与快速操作区相同的邀请弹窗
    this.showInviteModal(inviteCode, null, planTitle)

    wx.showToast({
      title: '已开启协作功能',
      icon: 'success'
    })
  },

  // 生成临时邀请码
  generateTempInviteCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let result = ''
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  },

  // 选择目的地
  selectDestination() {
    wx.navigateTo({
      url: '/subpackages/travel-planning/destination/index?mode=select',
      events: {
        selectDestination: (data) => {
          this.setData({
            'formData.destination': data.name,
            'formData.coordinates': data.coordinates
          })
          this.checkCanNextStep()
        }
      }
    })
  },

  // 地图选点
  openLocationPicker() {
    wx.chooseLocation({
      success: (res) => {
        this.setData({
          'formData.destination': res.name || res.address,
          'formData.coordinates': {
            latitude: res.latitude,
            longitude: res.longitude
          }
        })
        this.checkCanNextStep()
      },
      fail: (err) => {
        console.error('选择位置失败:', err)
        if (err.errMsg.includes('cancel')) {
          return
        }
        wx.showToast({
          title: '选择位置失败',
          icon: 'none'
        })
      }
    })
  },

  // 步骤导航
  nextStep() {
    if (!this.data.canNextStep) return
    
    const nextStep = this.data.currentStep + 1
    if (nextStep <= 3) {
      this.setData({ currentStep: nextStep })
      this.checkCanNextStep()
    }
  },

  prevStep() {
    const prevStep = this.data.currentStep - 1
    if (prevStep >= 1) {
      this.setData({ currentStep: prevStep })
      this.checkCanNextStep()
    }
  },

  // 保存计划
  async savePlan() {
    if (this.data.saving) return

    const self = this
    // {{ AURA-X: Modify - 根据编辑模式显示不同的加载文本. Approval: 寸止(ID:1738056000). }}
    this.setData({
      saving: true,
      loading: true,
      loadingText: this.data.isEditMode ? '正在更新计划...' : '正在创建计划...'
    })

    // 计算参与人数 - 确保数据类型正确
    const participantIndex = Number(this.data.participantIndex) || 0
    const participantCount = participantIndex + 1 // 索引从0开始，所以+1



    // 获取当前用户信息
    const userInfo = userManager.getUserInfo()

    // 如果用户头像是临时文件，先上传到云存储
    if (userInfo?.avatar && userInfo.avatar.startsWith('wxfile://')) {
      try {
        const uploadResult = await wx.cloud.callFunction({
          name: 'travel',
          data: {
            action: 'uploadAvatar',
            data: { tempFilePath: userInfo.avatar }
          }
        })

        if (uploadResult.result.success) {
          // 更新本地用户信息
          userInfo.avatar = uploadResult.result.data.avatarUrl
          userManager.updateUserInfo(userInfo)
        }
      } catch (error) {
        console.error('上传头像失败:', error)
      }
    }

    const planData = Object.assign({}, this.data.formData, {
      duration: this.data.duration,
      participantCount: participantCount,
      enableCollaboration: this.data.enableCollaboration
    })

    // {{ AURA-X: Modify - 根据编辑模式选择创建或更新计划. Approval: 寸止(ID:1738056000). }}
    // 使用云函数创建或更新计划（支持协作功能）
    const requestData = {
      name: 'travel',
      data: {
        action: self.data.isEditMode ? 'updateTravelPlan' : 'addTravelPlan',
        data: self.data.isEditMode ? { ...planData, id: self.data.planId } : planData
      }
    }

    wx.cloud.callFunction(requestData).then(function(result) {
      self.setData({
        saving: false,
        loading: false
      })

      if (result.result.success) {
        // {{ AURA-X: Modify - 根据编辑模式显示不同的成功提示. Approval: 寸止(ID:1738056000). }}
        wx.showToast({
          title: self.data.isEditMode ? '更新成功' : '创建成功',
          icon: 'success'
        })

        // 通知旅行规划页面刷新数据
        self.notifyTravelPageRefresh()

        // {{ AURA-X: Modify - 创建成功后直接返回，不弹窗. Approval: 寸止(ID:1738056000). }}
        // 创建成功后直接返回，不显示邀请弹窗
        setTimeout(function() {
          wx.navigateBack()
        }, 1500)
      } else {
        console.error(self.data.isEditMode ? '更新计划失败:' : '创建计划失败:', result.result.message)
        wx.showToast({
          title: result.result.message || (self.data.isEditMode ? '更新失败' : '创建失败'),
          icon: 'none'
        })
      }
    }).catch(function(error) {
      self.setData({
        saving: false,
        loading: false
      })
      console.error('创建计划异常:', error)
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      })
    })
  },

  // 取消创建
  cancelCreate() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消创建计划吗？已填写的信息将丢失。',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack()
        }
      }
    })
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '爱巢小记 - 创建旅行计划',
      path: '/subpackages/travel-planning/create-plan/index'
    }
  },

  // 通知旅行规划页面刷新
  notifyTravelPageRefresh() {
    try {
      // 使用data-manager的缓存清理方法
      const dataManager = require('../../../utils/data-manager.js').default
      dataManager.clearTravelPageCache()

      // 设置全局刷新标记
      wx.setStorageSync('travel_page_needs_refresh', Date.now())
    } catch (error) {
      // 静默处理通知失败
    }
  },

  // {{ AURA-X: Add - 创建成功后的邀请弹窗，会自动返回. Approval: 寸止(ID:1738056000). }}
  // 显示创建成功后的邀请弹窗
  showCreateSuccessInviteModal(inviteCode, planId, planTitle) {
    const link = `🎒 邀请你一起规划旅行：${planTitle}

📋 邀请码：${inviteCode}

💡 使用方法：
1. 打开"爱巢小记"小程序
2. 在旅行规划页面点击"加入计划"
3. 输入邀请码即可加入协作

快来一起规划精彩的旅程吧！✨`

    wx.showModal({
      title: '邀请好友协作',
      content: `计划创建成功！邀请码：${inviteCode}\n\n点击"复制邀请"将邀请信息复制到剪贴板，发送给好友即可邀请协作。`,
      confirmText: '复制邀请',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: link,
            success: () => {
              wx.showToast({
                title: '邀请信息已复制',
                icon: 'success'
              })
            }
          })
        }
        // 无论选择什么都返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1000)
      }
    })
  },

  // 显示邀请弹窗（创建过程中）
  showInviteModal(inviteCode, planId, planTitle) {
    const link = `🎒 邀请你一起规划旅行：${planTitle}

📋 邀请码：${inviteCode}

💡 使用方法：
1. 打开"爱巢小记"小程序
2. 在旅行规划页面点击"加入计划"
3. 输入邀请码即可加入协作

快来一起规划精彩的旅程吧！✨`

    // {{ AURA-X: Modify - 修改按钮文字和行为，取消时不返回页面. Approval: 寸止(ID:1738056000). }}
    wx.showModal({
      title: '邀请好友协作',
      content: `邀请码：${inviteCode}\n\n点击"复制邀请"将邀请信息复制到剪贴板，发送给好友即可邀请协作。`,
      confirmText: '复制邀请',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: link,
            success: () => {
              wx.showToast({
                title: '邀请信息已复制',
                icon: 'success'
              })
            }
          })
        }
        // 点击取消时不做任何操作，用户可以继续创建计划
      }
    })
  }
})
